import Mathlib.Data.Nat.Digits
import Mathlib.Data.Finset.Card
import Mathlib.Data.Finset.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.ModCases

-- AMC 12A 2020 Problem 4: Count 4-digit integers with all even digits divisible by 5

def is_even_digit (d : ℕ) : Prop := d ∈ ({0, 2, 4, 6, 8} : Finset ℕ)

def is_four_digit (n : ℕ) : Prop := 1000 ≤ n ∧ n ≤ 9999

def all_digits_even (n : ℕ) : Prop :=
  ∀ d ∈ Nat.digits 10 n, is_even_digit d

def valid_number (n : ℕ) : Prop :=
  is_four_digit n ∧ all_digits_even n ∧ n % 5 = 0

-- Main theorem: exactly 100 such numbers exist
theorem amc12a_2020_p4 :
  (Finset.filter valid_number (Finset.range 10000)).card = 100 := by
  sorry

-- Helper lemmas for each subgoal

-- SUBGOAL_001: Units digit must be 0
lemma units_digit_constraint (n : ℕ) (h1 : is_four_digit n) (h2 : all_digits_even n) (h3 : n % 5 = 0) :
  n % 10 = 0 := by
  -- n % 5 = 0 means n % 10 ∈ {0, 5}
  have h_mod_10 : n % 10 = 0 ∨ n % 10 = 5 := by
    have : n % 10 < 10 := Nat.mod_lt n (by norm_num)
    have : n % 5 = (n % 10) % 5 := by rw [Nat.mod_mod_of_dvd]; norm_num
    rw [h3] at this
    simp at this
    interval_cases (n % 10)
    · left; rfl
    · right; rfl
  -- But all digits are even, so units digit must be even
  have h_even : is_even_digit (n % 10) := by
    have : n % 10 ∈ Nat.digits 10 n := by
      rw [Nat.digits_def']
      · simp [Nat.digits]
        sorry -- Need to show n % 10 is in the digits list
      · norm_num
      · exact Nat.pos_of_ne_zero (ne_of_gt (h1.1))
    exact h2 (n % 10) this
  -- Even digits are {0, 2, 4, 6, 8}, so n % 10 ≠ 5
  cases h_mod_10 with
  | inl h => exact h
  | inr h =>
    rw [h] at h_even
    simp [is_even_digit] at h_even

-- SUBGOAL_002: Thousands digit has 4 choices
lemma thousands_digit_choices (n : ℕ) (h : is_four_digit n ∧ all_digits_even n) :
  (n / 1000) % 10 ∈ ({2, 4, 6, 8} : Finset ℕ) := by
  sorry

-- SUBGOAL_003: Hundreds digit has 5 choices
lemma hundreds_digit_choices (n : ℕ) (h : all_digits_even n) :
  (n / 100) % 10 ∈ ({0, 2, 4, 6, 8} : Finset ℕ) := by
  sorry

-- SUBGOAL_004: Tens digit has 5 choices
lemma tens_digit_choices (n : ℕ) (h : all_digits_even n) :
  (n / 10) % 10 ∈ ({0, 2, 4, 6, 8} : Finset ℕ) := by
  sorry

-- SUBGOAL_005: Multiplication principle gives 4 × 5 × 5 × 1 = 100
lemma counting_principle :
  4 * 5 * 5 * 1 = 100 := by
  sorry
